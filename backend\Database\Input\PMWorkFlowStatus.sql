IF NOT EXISTS (SELECT TOP 1 1 FROM PMWorkflowStatuses)
BEGIN
    SET IDENTITY_INSERT PMWorkflowStatuses ON;
    INSERT INTO PMWorkflowStatuses (Id, Status)
    VALUES
    (1, 'Initial'),
    (2, 'Sent for Review'),
    (3, 'Review Changes'),
    (4, 'Sent for Approval'),
    (5, 'Approval Changes'),
    (6, 'Approved');
    SET IDENTITY_INSERT PMWorkflowStatuses OFF;
    PRINT 'PMWorkflowStatuses data inserted successfully';
END
ELSE
BEGIN
    PRINT 'PMWorkflowStatuses data already exists, skipping insert';
END

-- Insert WBS-specific permissions if they don't exist
IF NOT EXISTS (SELECT TOP 1 1 FROM Permissions WHERE Category = 'WBS')
BEGIN
    INSERT INTO Permissions (Name, Description, Category)
    VALUES
    ('WBS.Read', 'View Work Breakdown Structure', 'WBS'),
    ('WBS.Create', 'Create WBS tasks and structures', 'WBS'),
    ('WBS.Update', 'Update WBS tasks and structures', 'WBS'),
    ('WBS.Delete', 'Delete WBS tasks and structures', 'WBS'),
    ('WBS.Approve', 'Approve WBS submissions', 'WBS'),
    ('WBS.ManageAll', 'Full WBS management across all projects', 'WBS');
    PRINT 'WBS permissions inserted successfully';
END
ELSE
BEGIN
    PRINT 'WBS permissions already exist, skipping insert';
END